﻿using Compass.Common.Data;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Site
{
    public partial class SiteTabs : IDisposable
    {
        private static readonly int SUMMARY_INDEX = 1;
        private static readonly int MANAGE_INDEX = 2;
        private static readonly int ADD_CHILD_INDEX = 3;
        private static readonly int EDIT_INDEX = 4;
        private static readonly int SUPPORT_INDEX = 5;
        private static readonly int REPORT_INDEX = 6;
        private static readonly int USER_INDEX = 7;

        private int currentTab = SUMMARY_INDEX;
        private Type? currentTabComponent;

        private string currentSiteName = string.Empty;
        private string studentGroupHierarchy = string.Empty;

        private readonly Dictionary<int, Type> tabComponents = new()
        {
            { SUMMARY_INDEX, typeof(SiteSummaryComponent) },
            { MANAGE_INDEX, typeof(SiteManageComponent) },
            { ADD_CHILD_INDEX, typeof(StudentGroupAddEditComponent) },
            { EDIT_INDEX, typeof(SiteEditComponent) },
            { SUPPORT_INDEX, typeof(SiteSummaryComponent) },
            { REPORT_INDEX, typeof(SiteReportsComponent) },
            { USER_INDEX, typeof(SiteUsers) }
        };

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionDataObserver.AddStateChangeAsyncListeners(UpdateSiteName);
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                studentGroupHierarchy = commonSessionData.StudentGroupHierarchy;

                currentSiteName = commonSessionData.SelectedEntityName;
                currentTab = SUMMARY_INDEX;
                // Initialize with the first tab's component
                currentTabComponent = tabComponents[currentTab];
            }
        }
        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task UpdateSiteName()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.currentSiteName = commonSessionData.SelectedEntityName;
                StateHasChanged();
            }
        }

        protected void ChangeTab(int tabIndex)
        {
            currentTab = tabIndex;
            currentTabComponent = tabComponents[currentTab];
        }

        public void Dispose()
        {
            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(UpdateSiteName);
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
