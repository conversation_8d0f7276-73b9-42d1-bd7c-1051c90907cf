namespace Compass.DECA.DTOs
{
    public class DecaStudentContactDto
    {
        public long Id { get; set; }
        public long StudentId { get; set; }
        public long OrganizationId { get; set; }
        public char Status { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? MiddleName { get; set; }
        public char ChildContactType { get; set; }
        public char? Gender { get; set; }
        public string? LanguagePreference { get; set; }
        public string? ParentEducationLevel { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public char? State { get; set; }
        public string? Zip { get; set; }
        public string? County { get; set; }
        public string? Country { get; set; }
        public string? PhonePrimary { get; set; }
        public string? Email { get; set; }
        public string? ContactNote { get; set; }
        public Guid? ChildPlusAdultPersonId { get; set; }
        public DateTime? ChildPlusLastUpdate { get; set; }
        public string? ModId { get; set; }
        public DateTime? ModTs { get; set; }
    }
}
