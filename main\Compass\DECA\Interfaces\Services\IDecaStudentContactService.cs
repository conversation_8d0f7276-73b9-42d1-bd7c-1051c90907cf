using Compass.DECA.DTOs;

namespace Compass.DECA.Interfaces.Services
{
    public interface IDecaStudentContactService
    {
        /// <summary>
        /// Gets all contacts for a specific student
        /// </summary>
        /// <param name="studentId">The ID of the student</param>
        /// <returns>A list of DecaStudentContactDto objects ordered by last name, then first name</returns>
        Task<List<DecaStudentContactDto>> GetContactsByStudentIdAsync(long studentId);

        /// <summary>
        /// Gets a contact by ID
        /// </summary>
        /// <param name="id">The ID of the contact</param>
        /// <returns>The DecaStudentContactDto object if found, null otherwise</returns>
        Task<DecaStudentContactDto?> GetByIdAsync(long id);

        /// <summary>
        /// Creates a new student contact
        /// </summary>
        /// <param name="contact">The contact data to create</param>
        /// <returns>The created DecaStudentContactDto object</returns>
        Task<DecaStudentContactDto> CreateAsync(DecaStudentContactDto contact);

        /// <summary>
        /// Updates an existing student contact
        /// </summary>
        /// <param name="contact">The contact data to update</param>
        /// <returns>The updated DecaStudentContactDto object</returns>
        Task<DecaStudentContactDto> UpdateAsync(DecaStudentContactDto contact);

        /// <summary>
        /// Deletes a student contact
        /// </summary>
        /// <param name="id">The ID of the contact to delete</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        Task<bool> DeleteAsync(long id);
    }
}
