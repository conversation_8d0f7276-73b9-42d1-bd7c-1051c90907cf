﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Deca.Models
{
    [Table("edeca_student_contacts")]
    public class DecaStudentContact
    {
        [Key]
        [Column("id")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Column("mod_id")]
        [MaxLength(450)]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("student_id")]
        [Required]
        public long StudentId { get; set; }

        [Column("organization_id")]
        [Required]
        public long OrganizationId { get; set; }

        [Column("status")]
        [Required]
        [MaxLength(1)]
        public char Status { get; set; }

        [Column("first_name")]
        [Required]
        [MaxLength(20)]
        public string FirstName { get; set; } = string.Empty;

        [Column("last_name")]
        [Required]
        [MaxLength(20)]
        public string LastName { get; set; } = string.Empty;

        [Column("middle_name")]
        [MaxLength(20)]
        public string? MiddleName { get; set; }

        [Column("child_contact_type")]
        [Required]
        [MaxLength(1)]
        public char ChildContactType { get; set; }

        [Column("gender")]
        [MaxLength(1)]
        public char? Gender { get; set; }

        [Column("language_preference")]
        [MaxLength(10)]
        public string? LanguagePreference { get; set; }

        [Column("parent_education_lvl")]
        [MaxLength(10)]
        public string? ParentEducationLevel { get; set; }

        [Column("address1")]
        [MaxLength(50)]
        public string? Address1 { get; set; }

        [Column("address2")]
        [MaxLength(50)]
        public string? Address2 { get; set; }

        [Column("city")]
        [MaxLength(30)]
        public string? City { get; set; }

        [Column("state")]
        [MaxLength(2)]
        public char? State { get; set; }

        [Column("zip")]
        [MaxLength(12)]
        public string? Zip { get; set; }

        [Column("county")]
        [MaxLength(35)]
        public string? County { get; set; }

        [Column("country")]
        [MaxLength(30)]
        public string? Country { get; set; }

        [Column("phone_primary")]
        [MaxLength(24)]
        public string? PhonePrimary { get; set; }

        [Column("email")]
        [MaxLength(512)]
        [EmailAddress]
        public string? Email { get; set; }

        [Column("contact_note")]
        [MaxLength(200)]
        public string? ContactNote { get; set; }

        //TODO Need to look into child plus import on how this will work
        [Column("child_plus_adult_person_i_d")]
        public Guid? ChildPlusAdultPersonId { get; set; }

        [Column("child_plus_last_update")]
        public DateTime? ChildPlusLastUpdate { get; set; }
    }
}