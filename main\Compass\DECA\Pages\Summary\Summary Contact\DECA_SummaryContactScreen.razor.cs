using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Compass.DECA.DTOs;
using Microsoft.AspNetCore.Components;

namespace Compass.DECA.Pages.Summary.Summary_Contact
{
    public partial class DECA_SummaryContactScreen : ComponentBase, IDisposable
    {
        [Inject] public required UserSessionService UserSessionService { get; set; }
        [Inject] public required UserAccessor UserAccessor { get; set; }
        [Inject] public required CurrentCultureObserver CurrentLanguageObserver { get; set; }
        [Inject] public required CultureService CultureService { get; set; }

        private bool isLoading = true;
        private ApplicationUser? _currentUser;
        private string? _currentUserId;
        private long? _currentStudentId;
        private string studentName = "Unknown Student";
        private List<DecaStudentContactDto>? contacts;

        protected override async Task OnInitializedAsync()
        {
            isLoading = true;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData?.CurrentStudentId != null)
            {
                _currentStudentId = commonSessionData.CurrentStudentId;
                await LoadStudentContacts();
            }
            isLoading = false;
        }

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }
            return commonSessionData;
        }

        private async Task LoadStudentContacts()
        {
            if (!_currentStudentId.HasValue)
            {
                return;
            }
            contacts = [new() { Id = 1047625, FirstName = "Christos", LastName = "Tzigas", MiddleName = "Andreas", Status = 'I', ChildContactType = 'F', LanguagePreference = "Greek", StudentId = _currentStudentId.Value, OrganizationId = 1 }];
        }

        private static string GetContactTypeDisplay(char contactType) => contactType switch
        {
            'F' => "Father",
            'M' => "Mother",
            'G' => "Guardian",
            'O' => "Other",
            '\0' => "",
            _ => string.IsNullOrWhiteSpace(contactType.ToString()) ? "" : contactType.ToString()
        };

        private void UpdateLocalizedValues()
        {
            CultureService.SetCulture(CurrentLanguageObserver.GetCurrentCulture());
            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
            GC.SuppressFinalize(this);
        }
    }
}