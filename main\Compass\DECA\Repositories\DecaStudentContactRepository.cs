using Compass.Common.Data;
using Compass.DECA.DTOs;
using Compass.DECA.Interfaces.Repositories;
using Compass.Deca.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.DECA.Repositories
{
    public class DecaStudentContactRepository : IDecaStudentContactRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public DecaStudentContactRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<List<DecaStudentContactDto>> GetContactsByStudentIdAsync(long studentId)
        {
            try
            {
                using ApplicationDbContext dbContext = _contextFactory.CreateDbContext();
                List<DecaStudentContact> entities = await dbContext.DecaStudentContacts
                    .Where(c => c.StudentId == studentId)
                    .OrderBy(c => c.LastName)
                    .ThenBy(c => c.FirstName)
                    .ToListAsync();

                return entities.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting contacts for student {studentId}: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentContactDto?> GetByIdAsync(long id)
        {
            try
            {
                using ApplicationDbContext dbContext = _contextFactory.CreateDbContext();
                DecaStudentContact? entity = await dbContext.DecaStudentContacts.FindAsync(id);
                return entity == null ? null : MapToDto(entity);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting contact by ID {id}: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentContactDto> CreateAsync(DecaStudentContactDto contact)
        {
            try
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                DecaStudentContact entity = MapToEntity(contact);
                entity.ModId = userId;
                entity.ModTs = DateTime.Now;

                using ApplicationDbContext dbContext = _contextFactory.CreateDbContext();
                await dbContext.DecaStudentContacts.AddAsync(entity);
                await dbContext.SaveChangesAsync();

                return MapToDto(entity);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating student contact: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentContactDto> UpdateAsync(DecaStudentContactDto contact)
        {
            try
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                DecaStudentContact entity = MapToEntity(contact);
                entity.ModId = userId;
                entity.ModTs = DateTime.Now;

                using ApplicationDbContext dbContext = _contextFactory.CreateDbContext();
                dbContext.DecaStudentContacts.Update(entity);
                await dbContext.SaveChangesAsync();

                return MapToDto(entity);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating student contact: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                using ApplicationDbContext dbContext = _contextFactory.CreateDbContext();
                DecaStudentContact? entity = await dbContext.DecaStudentContacts.FindAsync(id);
                if (entity == null)
                {
                    return false;
                }

                dbContext.DecaStudentContacts.Remove(entity);
                await dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting student contact {id}: {ex.Message}");
                throw;
            }
        }

        private static DecaStudentContactDto MapToDto(DecaStudentContact entity)
        {
            return new DecaStudentContactDto
            {
                Id = entity.Id,
                StudentId = entity.StudentId,
                OrganizationId = entity.OrganizationId,
                FirstName = entity.FirstName,
                LastName = entity.LastName,
                MiddleName = entity.MiddleName,
                ChildContactType = entity.ChildContactType,
                Gender = entity.Gender,
                LanguagePreference = entity.LanguagePreference,
                ParentEducationLevel = entity.ParentEducationLevel,
                Address1 = entity.Address1,
                Address2 = entity.Address2,
                City = entity.City,
                State = entity.State,
                Zip = entity.Zip,
                County = entity.County,
                Country = entity.Country,
                PhonePrimary = entity.PhonePrimary,
                Email = entity.Email,
                ContactNote = entity.ContactNote,
                ChildPlusAdultPersonId = entity.ChildPlusAdultPersonId,
                ChildPlusLastUpdate = entity.ChildPlusLastUpdate,
                ModId = entity.ModId,
                ModTs = entity.ModTs
            };
        }

        private static DecaStudentContact MapToEntity(DecaStudentContactDto dto)
        {
            return new DecaStudentContact
            {
                Id = dto.Id,
                StudentId = dto.StudentId,
                OrganizationId = dto.OrganizationId,
                FirstName = dto.FirstName,
                LastName = dto.LastName,
                MiddleName = dto.MiddleName,
                ChildContactType = dto.ChildContactType,
                Gender = dto.Gender,
                LanguagePreference = dto.LanguagePreference,
                ParentEducationLevel = dto.ParentEducationLevel,
                Address1 = dto.Address1,
                Address2 = dto.Address2,
                City = dto.City,
                State = dto.State,
                Zip = dto.Zip,
                County = dto.County,
                Country = dto.Country,
                PhonePrimary = dto.PhonePrimary,
                Email = dto.Email,
                ContactNote = dto.ContactNote,
                ChildPlusAdultPersonId = dto.ChildPlusAdultPersonId,
                ChildPlusLastUpdate = dto.ChildPlusLastUpdate,
                ModId = dto.ModId,
                ModTs = dto.ModTs
            };
        }
    }
}