using Compass.DECA.DTOs;
using Compass.DECA.Interfaces.Repositories;
using Compass.DECA.Interfaces.Services;
using Serilog;

namespace Compass.DECA.Services
{
    public class DecaStudentContactService : IDecaStudentContactService
    {
        private readonly IDecaStudentContactRepository _repository;

        public DecaStudentContactService(IDecaStudentContactRepository repository)
        {
            _repository = repository;
        }

        public async Task<List<DecaStudentContactDto>> GetContactsByStudentIdAsync(long studentId)
        {
            try
            {
                return await _repository.GetContactsByStudentIdAsync(studentId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting contacts for student {StudentId}", studentId);
                throw;
            }
        }

        public async Task<DecaStudentContactDto?> GetByIdAsync(long id)
        {
            try
            {
                return await _repository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting contact {Id}", id);
                throw;
            }
        }

        public async Task<DecaStudentContactDto> CreateAsync(DecaStudentContactDto contact)
        {
            try
            {
                return await _repository.CreateAsync(contact);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating student contact");
                throw;
            }
        }

        public async Task<DecaStudentContactDto> UpdateAsync(DecaStudentContactDto contact)
        {
            try
            {
                return await _repository.UpdateAsync(contact);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating student contact {Id}", contact.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                return await _repository.DeleteAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting student contact {Id}", id);
                throw;
            }
        }
    }
}
